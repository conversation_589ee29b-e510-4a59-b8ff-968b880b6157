'use client';

import React, {useContext, useEffect, useState} from "react";
import clsx from "clsx";
import {useDashboardLayout} from "@/context/DashboardLayoutContext";
import {useRouter} from "next/navigation";
import strapi from "@/app/api/strapi";
import {UserContext} from "@/context/UserProvider";
import ViewFullImageModal from "@/components/dashboard/ViewFullImageModal";
import dynamic from 'next/dynamic';
import StarPointPopup from "@/components/StarPointPopup";
import OutStreakPopup from "@/components/OutStreakPopup";
import ContinueStreak from "@/components/ContinueStreak";
import TutorialStreak from "@/components/TutorialStreak";
import ReportStreak from "@/components/ReportStreak";
import {useNotification} from "@/context/NotificationContext";
import {toast} from "react-hot-toast";
import {useScreenSize} from "@/hooks/useScreenSize";



const KaTeXRenderer = dynamic(() => import('@/components/katex-renderer'), {
    ssr: false,
    loading: () => <p>Đang tải câu hỏi...</p>,
});
export default function StreakPage() {
    const router = useRouter();
    let [indexQuestionActive, setIndexQuestionActive] = useState(0);
    const { showNotification } = useNotification();
    const [questions, setQuestions] = useState([]);
    const [lengthProgress, setLengthProgress] = useState(0);
    const [answered, setAnswered] = useState([]);
    const [isQuestion2, setIsQuestion2] = useState(false);
    const [time, setTime] = useState(0);
    const [streakId, setStreakId] = useState(null);
    const [total, setTotal] = useState(1);
    const [isStart, setIsStart] = useState(false);
    const [isFinish, setIsFinish] = useState(false);
    const [dataFinish, setDataFinish] = useState({});
    const [isViewFullImage, setIsViewFullImage] = useState(false);
    const [isViewStarPoint, setIsViewStarPoint] = useState(false);
    const [isViewOutPopup, setIsViewOutPopup] = useState(false);
    const [isViewTutorial, setIsViewTutorial] = useState(false);
    const [isContinueStreak, setIsContinueStreak] = useState(false);
    const [isViewReportStreak, setIsViewReportStreak] = useState(false);
    const [isUseStarPoint, setIsUseStarPoint] = useState(false);
    const [isViewAnswer, setIsViewAnswer] = useState(false);
    const {getUser} = useContext(UserContext);
    const [dayActive, setDayActive] = useState(null);
    const [userAnswer, setUserAnswer] = useState({});
    const [questionActive, setQuestionActive] = useState(null);
    const [streakData, setStreakData] = useState({});
    const [timer, setTimer] = useState(null);
    const screenSize = useScreenSize();

    const {setTitle, keySearch, setKeySearch, setIsSearch, setIsDetail, setIsTurnLive} = useDashboardLayout();

    useEffect(() => {
        setTitle("Streak");
        setIsSearch(false);
        setIsDetail(false);
        setIsTurnLive(true);
        setKeySearch(keySearch);
        return () => {
            setIsSearch(false);
            setIsTurnLive(false);
        };

    }, []);
    useEffect(() => {
        const startStreak = async () => {
            await getData(); //Lấy thông tin của trang chủ thống kê trước khi bắt đầu
            await getStreakQuestion(); // lấy thông tin streakId của ngày hôm nay => Lấy câu hỏi của streak
            await getTotalRollup(); // Lấy chuỗi streak ở trang chủ
        }
        startStreak();

    }, []);
    useEffect(() => {

    }, [streakId]);
    useEffect(() => {
        clearInterval(timer);
        if (isQuestion2) {
            setTime(time);
        }else {
            if (isStart && dayActive) {
                let t = setInterval(() => {
                    setTime(time + 1);
                    if (time % 10 === 0) {
                        strapi.streak.updateTime({documentId: dayActive.documentId, time:time});
                    }
                }, 1000);
                setTimer(t);
            }
        }
        return () => {
            clearInterval(timer);
        }
    }, [isStart,time, isQuestion2]);
    useEffect(() => {
        if (streakData.dayOfWeek && streakData.dayOfWeek.length > 0) {
            const f =  streakData.dayOfWeek.filter((value,index) => value.isActive);
            if (f && f.length > 0) {
                setDayActive(f[0]);
                setTime(f[0].time === null ? 0 : f[0].time);

            }
        }
    },[streakData]);
    useEffect(  () => {
        if (streakId) {
            getDataFinish();
            getTotalRollup();
        }
    }, [isFinish]);
    useEffect(() => {
        getQuestionByStreak();
    }, [streakId]);
    const getDataFinish = async () => {
        const user = getUser();
        const res = await strapi.streak.getDataFinish({user_id : user.id, streak_question_id: streakId});
        if (res && res.data) {
            const d = {...res.data.data[0]};
            setDataFinish(d);

        }
    }
    useEffect(() => {
        setQuestionActive(questions[indexQuestionActive]);
        setLengthProgress((indexQuestionActive + 1) * 100 / questions.length);
    }, [indexQuestionActive]);
    const getQuestionByStreak = async () => {
        if (streakId && dayActive) {
            const  user = getUser();
            const res = await strapi.streak.getQuestionByStreak(streakId);
            if (res?.data) {
                setQuestions(res?.data.data);

                const req = {user: user.id, streakId: streakId};
                const r = await strapi.streak.getAnswerStreakByUser(req);
                if (r && r.data && r.data.data.length > 0) {
                    const ans = r.data.data;
                    const a = ans.filter(value => value.is_star_point);
                    if (a.length > 0) {
                        setIsUseStarPoint(true);
                    }
                    setIsContinueStreak(true);
                    setIsStart(true)

                    if (dayActive.isJoin !== 1) {
                        if (r.data.data.length === 2) {
                            setIsQuestion2(true);
                            setIndexQuestionActive(2);
                        }if (r.data.data.length === 3) {
                            setIsStart(false)
                        } else{
                            setIndexQuestionActive(r.data.data.length);
                        }
                    }else {
                        setIsStart(false);
                        setIsFinish(false);
                    }

                }else {
                    if (dayActive.documentId) {
                        setIsContinueStreak(true);
                        setIndexQuestionActive(0);
                        setQuestionActive(res?.data.data[0]);
                        setIsStart(true)
                    }
                }

            }
        }
    }
    const getStreakQuestion = async () => {
        const date = new Date();
        const user_ = getUser();
        const truncatedDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        const dd = String(truncatedDate.getDate()).padStart(2, '0');
        const mm = String(truncatedDate.getMonth() + 1).padStart(2, '0');
        const yyyy = truncatedDate.getFullYear();
        const formatted = `${yyyy}-${mm}-${dd}`;
        const orders = await strapi.orders.getOrderByUser(user_.id,'course');
        const res = await strapi.streak.getStreak(formatted,orders.data[0].course.id);
        if (res?.data && res?.data.data.length > 0) {
            setStreakId(res.data.data[0].id);
        }else {
            showNotification({
                type: "error",
                title: "Streak không tìm thấy",
                message: "Nhắn cho Ba để thêm thông tin streak nhé bây!",
            });
        }
    }
    const getTotalRollup = async () => {
        // get-total-rollup
        try {
            const user_ = getUser();
            const res = await strapi.streak.getTotalRollup({user_id: user_.id});
            if (res?.data?.data !== null && res?.data?.data !== undefined) {
                setTotal(res?.data?.data);
            }
        } catch (error) {
            console.error("Lỗi khi lấy data:", error);
            if (error.response) {
                console.error("Response trả về:", error.response);
            }

        }
    }
    const getData = async  () => {
        try {
            const user_ = getUser();
            const res = await strapi.streak.getDataByUser({user_id: user_.id});
            if (res?.data?.data) {
                const  a = {
                    dayOfWeek: [],
                    totalCount: 1,
                    trueCount: 1,
                    timeCount: 3665,
                    rankingCount: '51/100',
                    questions: [
                        {
                            content: "Phương pháp nấu rượu truyền thống bằng cách lên men tinh bột vẫn được nhiều người dân sử dụng từ xưa đến nay. Tinh bột sau khi thủy phân, lên men thì được đem chưng cất để thu lấy ethanol. Trong quá trình chưng cất, chất lỏng ban đầu thu được có vị rất nồng, sau đó nhạt dần và cuối cùng có vị chua. Để rượu ngon, khi chưng cất người ta thường bỏ đi khoảng 100 - 200 mL chất lỏng chảy ra đầu tiên. Biết khối lượng riêng của ethanol d=0,8gam/cm. Hỏi trong những câu sau:",
                            a: "Phân tích mẫu vật từ thiên thạch rơi xuống Trái Đất, các nhà khoa học nhận nguyên tố Q với ba đồng vị",
                            b: "Các nhà khoa học nhận nguyên tố Q",
                            c: "Phân tích mẫu vật từ thiên thạch rơi xuống Trái Đất",
                            d: "Báo cáo sơ bộ ghi nhầm nguyên tử khối trung bình, số hiệu nguyên tử gần với Fe",
                            order: 1,
                            point: 10,
                            // imagePath: "https://be.ongbadayhoa.com/uploads/thumbnail_c07407799afe225385ac6991edb58704.jpg"
                            image_path: "http://localhost:1337/uploads/anh.png",
                            correct_answer_type: "C",
                            correct_answer: "Phân tích mẫu vật từ thiên thạch rơi xuống Trái Đất, các nhà khoa học nhận nguyên tố Q với ba đồng vị",
                            explain: "**Giải:**\n\n**Cho:** $m_{\\ce{NaOH}} = 20$ g, $V_{dd} = 500$ ml $= 0.5$ L, $M_{\\ce{NaOH}} = 40$ g/mol\n\n**Tính số mol NaOH:**\n$n_{\\ce{NaOH}} = \\frac{m}{M} = \\frac{20}{40} = 0.5$ mol\n\n**Tính nồng độ mol:**\n$C_M = \\frac{n}{V} = \\frac{0.5}{0.5} = 1.0$ M\n\n**Đáp án:** 1.0 M"
                        },

                    ]
                }
                a.dayOfWeek = res?.data?.data;
                setStreakData(a);
                setTime(a.timeCount);
            }
        } catch (error) {
            console.error("Lỗi khi lấy data:", error);
            if (error.response) {
                console.error("Response trả về:", error.response);
            }
        }
    };
    const closeStreakHomePage = () => {
        router.push("/quan-ly");
    }
    const closeStreak = () => {
        setIsViewOutPopup(true)

    }
    const clickStart = async () => {
        if ( dayActive.documentId ) {
            return;
        }
        const user_ = getUser();
        const data = {
            isJoin: 0,
            user_id: user_.id,
            streak_question_id: streakId
        }
        const res = await strapi.streak.createStreak(data);
        if (res?.status === 201) {
            debugger
            let a = {...dayActive,documentId: res.data.data.documentId};
            setDayActive(a);
            // getData();
            // getTotalRollup();
            setIsStart(true);
            if (questions.length === 0) {
                await getQuestionByStreak();
                setQuestionActive(questions[0]);
                setIndexQuestionActive(0);
            }else {
                setQuestionActive(questions[0]);
                setIndexQuestionActive(0);
            }
        }
    }
    const convertSecond2Text = (seconds) => {
        const hrs = Math.floor(seconds / 3600);
        const mins = Math.floor((seconds % 3600) / 60);
        const secs = Math.floor(seconds % 60);
        const pad = (num) => String(num).padStart(2, '0');
        return `${pad(hrs)}:${pad(mins)}:${pad(secs)}`;
    }
    const closeViewFullImage = () => {
        setIsViewFullImage(false);
    }
    const viewFullImage = () => {
        setIsViewFullImage(true);
    }
    const handelUserAnswer = (answer) => {
        if (answer) {
            const user_ = getUser();
            const a = {
                ...userAnswer,
                user: user_.id,
                streak_question_id: streakId,
                answer: answer,
                is_correct: answer === questionActive.correct_answer_type,
                question: questionActive.id,
            }
            setUserAnswer(a);
        }
    }
    const useStarPoint = () => {
        if (userAnswer.is_star_point) {
            const  a = {...userAnswer,is_star_point:false}
            setUserAnswer(a);
        }else {
            setIsViewStarPoint(true)
        }

    }
    const ViewAnswerAndNextQuestion = async () => {
        if (indexQuestionActive <= questions.length -1) {
            debugger
            const  ans = !isViewAnswer
            if (ans) {
                // luu xuong database
                if (!userAnswer.answer) {
                    // toast.error('Chưa điền đáp án nào');
                    toast.error('Điền đáp án đã nhé!');
                    return;
                }
                const r =  await strapi.streak.saveQuestionAnswer(userAnswer);
                if (r && r.status === 201 && !isUseStarPoint) {
                    setIsUseStarPoint(userAnswer.is_star_point);
                }
                setIsViewAnswer(ans);

            }else {
                let i =  indexQuestionActive + 1;
                // reset lại useranswer
                const a = {answer: "",is_correct: false,is_star_point: false}
                setUserAnswer(a);

                if (i === 2 ) {
                    setIsQuestion2(true);
                    setIsViewAnswer(false);
                }else if (i === 3){
                    setIsViewAnswer(false);
                    strapi.streak.updateTime({documentId: dayActive.documentId, time:time});
                    clearInterval(timer);
                    //Hoàn thành
                }else {
                    setIndexQuestionActive(i);
                    setQuestionActive(questions[i]);
                    setIsViewAnswer(ans);
                }
            }

        }
    }
    const reportStreak = () => {
        setIsViewReportStreak(true);
    }
    const handelContinueStreak = () => {
      setIsQuestion2(false);
      setIndexQuestionActive(2);
      setQuestionActive(questions[2]);
      setTime(time);
    }
    const finishStreak = async () => {
        debugger
        const d = {documentId: dayActive.documentId, isJoin: true, time: time};
        const res = await strapi.streak.finishStreak(d);
        getData();
        setIsFinish(true);
        setIsStart(false);

    }
    const openTutorial = () => {
        setIsViewTutorial(true);
    }
    if (!isStart) {
        return (
            <>
                <div className="flex flex-col items-center justify-center h-full min-h-[calc(100vh-160px)] absolute top-0 left-0 bg-white w-full"
                     style={{zIndex: 100}}>
                    <div className="max-w-[1200px] w-full flex flex-col h-full">
                        <div className="header_streak h-[76px] p-xl text-center w-full">
                            <div className="w-full flex flex-row justify-between h-[44px]">
                                <div className="close_streak p-[8px] w-[36px] h-full cursor-pointer" onClick={closeStreakHomePage}>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <path d="M18 6L6 18M6 6L18 18" stroke="#A4A7AE" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                </div>
                                <div className="title_streak text-center items-center">
                                    <p className={clsx(
                                        "text-primary-900 font-semibold ",
                                        screenSize?.lte960 ? "text-lg leading-lg" : "text-xl leading-xl"
                                    )}>{isFinish ? 'Kết quả hôm nay' : 'Streak'}</p>
                                </div>
                                <div className="tooltip_streak h-full w-[44px] p-lg cursor-pointer" onClick={openTutorial}>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                        <g clipPath="url(#clip0_4967_7454)"><path d="M7.57533 7.49996C7.77125 6.94302 8.15795 6.47338 8.66695 6.17424C9.17596 5.87509 9.77441 5.76574 10.3563 5.86555C10.9382 5.96536 11.466 6.2679 11.8462 6.71957C12.2264 7.17124 12.4345 7.74289 12.4337 8.33329C12.4337 9.99996 9.93366 10.8333 9.93366 10.8333M10.0003 14.1666H10.0087M18.3337 9.99996C18.3337 14.6023 14.6027 18.3333 10.0003 18.3333C5.39795 18.3333 1.66699 14.6023 1.66699 9.99996C1.66699 5.39759 5.39795 1.66663 10.0003 1.66663C14.6027 1.66663 18.3337 5.39759 18.3337 9.99996Z" stroke="#535862" strokeWidth="1.66667" strokeLinecap="round" strokeLinejoin="round"/></g>
                                        <defs>
                                            <clipPath id="clip0_4967_7454">
                                                <rect width="20" height="20" fill="white"/>
                                            </clipPath>
                                        </defs>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div className="content_streak w-full py-6xl px-xl gap-y-3xl flex flex-col items-center relative">
                            <div className="count_all items-center flex flex-row">
                                <div>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="69" height="68" viewBox="0 0 69 68" fill="none">
                                        <path d="M49.3743 42.9866C49.3743 51 44.6173 57.4961 38.7493 57.4961C32.8813 57.4961 28.1244 51 28.1244 42.9866C28.1244 34.9733 32.8813 28.4772 38.7493 28.4772C44.6173 28.4772 49.3743 34.9733 49.3743 42.9866Z" fill="#FDE272"/>
                                        <path d="M55.6929 35.664C51.2446 24.3881 35.4063 23.7801 39.2313 7.39131C39.5146 6.17528 38.1829 5.23562 37.1063 5.87127C26.8213 11.7856 19.4263 23.6419 25.6313 39.1739C26.1413 40.4452 24.6113 41.6336 23.5063 40.8045C18.378 37.0182 17.8397 31.5737 18.293 27.6769C18.463 26.2398 16.5363 25.5488 15.7147 26.7372C13.788 29.6115 11.833 34.2545 11.833 41.2467C12.9097 56.7234 26.3113 61.477 31.128 62.085C38.0129 62.9418 45.4646 61.6981 50.8196 56.9169C56.7129 51.5829 58.8662 43.0707 55.6929 35.664ZM29.3996 49.5654C33.4796 48.5981 35.5763 45.7239 36.1429 43.1813C37.0779 39.2292 33.423 35.36 35.8879 29.114C36.8229 34.2821 45.1529 37.5157 45.1529 43.1536C45.3796 50.1458 37.6163 56.1431 29.3996 49.5654Z" fill="#FF692E"/>
                                    </svg>
                                </div>
                                <div className="number_count ">
                                    <p className={clsx(
                                        "text-display-2xl font-semibold leading-display-2xl",
                                        total > 0 ? 'text-utility-orange-dark-400' : 'text-utility-gray-400'
                                    )}>{total}</p>
                                </div>
                            </div>
                            <div className="diem_danh max-w-[420px] w-full gap-xl p-xl flex flex-col items-center text-center rounded-[8px] border border-utility-gray-300">
                                <div className="thu_trong_tuan flex flex-row justify-between w-full">
                                    {
                                        streakData?.dayOfWeek?.map((value,index) => {
                                            return  <div className="item_thu flex flex-col gap-y-md w-[40px] " key={index}>
                                                <div className="w-full h-[40px] rounded-full">
                                                    {

                                                        value.isJoin === null ?
                                                            (
                                                                value.isActive ?
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none">
                                                                        <circle cx="20" cy="20" r="19" fill="#FAFAFA" stroke="#299D55" strokeWidth="2" strokeDasharray="4 4"/>
                                                                    </svg>
                                                                    :
                                                                    <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none">
                                                                        <circle cx="20" cy="20" r="20" fill="#F5F5F5"/>
                                                                    </svg>
                                                            )

                                                            : value.isJoin ?
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none">
                                                                    <path d="M28.7496 25.2864C28.7496 30.0002 25.9514 33.8214 22.4996 33.8214C19.0479 33.8214 16.2497 30.0002 16.2497 25.2864C16.2497 20.5727 19.0479 16.7515 22.4996 16.7515C25.9514 16.7515 28.7496 20.5727 28.7496 25.2864Z" fill="#FFCE51"/>
                                                                    <path d="M32.4664 20.979C29.8498 14.3461 20.5331 13.9885 22.7831 4.34801C22.9498 3.6327 22.1665 3.07996 21.5331 3.45387C15.4831 6.93289 11.1332 13.9072 14.7831 23.0437C15.0831 23.7915 14.1832 24.4905 13.5332 24.0028C10.5165 21.7756 10.1998 18.573 10.4665 16.2807C10.5665 15.4353 9.43316 15.0289 8.94983 15.728C7.8165 17.4187 6.6665 20.1499 6.6665 24.2629C7.29984 33.3669 15.1831 36.1631 18.0165 36.5208C22.0665 37.0248 26.4498 36.2932 29.5998 33.4807C33.0664 30.3431 34.3331 25.3359 32.4664 20.979ZM16.9998 29.1563C19.3998 28.5873 20.6331 26.8966 20.9665 25.4009C21.5165 23.0762 19.3665 20.8002 20.8165 17.1261C21.3665 20.1662 26.2665 22.0682 26.2665 25.3847C26.3998 29.4977 21.8331 33.0255 16.9998 29.1563Z" fill="#FF7324"/>
                                                                </svg>
                                                                :
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40" fill="none">
                                                                    <path d="M20 3.33301C29.2047 3.33302 36.6669 10.1197 36.667 18.4912C36.667 23.0455 34.4551 27.1267 30.959 29.9053C31.0376 30.2792 31.0811 30.6671 31.0811 31.0645C31.0809 34.1583 28.5723 36.6666 25.4785 36.667H25V33.333C24.9998 32.4127 24.2534 31.667 23.333 31.667C22.4128 31.6672 21.6672 32.4128 21.667 33.333V36.667H18.333V33.333C18.3328 32.4128 17.5872 31.6672 16.667 31.667C15.7466 31.667 15.0002 32.4127 15 33.333V36.667H14.7197C11.626 36.6665 9.11834 34.1582 9.11816 31.0645C9.11816 30.715 9.15069 30.3728 9.21191 30.041C5.61656 27.2607 3.33301 23.1197 3.33301 18.4912C3.33306 10.1197 10.7953 3.33301 20 3.33301ZM13.499 14.8535C11.0145 14.854 9.0014 16.6856 9.00098 18.9453C9.00098 21.2053 11.0142 23.0386 13.499 23.0391C15.9843 23.0391 18 21.2056 18 18.9453C17.9996 16.6853 15.984 14.8535 13.499 14.8535ZM26.833 14.8535C24.3483 14.8539 22.3344 16.6855 22.334 18.9453C22.334 21.2054 24.348 23.0387 26.833 23.0391C29.3181 23.0389 31.333 21.2055 31.333 18.9453C31.3326 16.6854 29.3179 14.8537 26.833 14.8535Z" fill="#D5D7DA"/>
                                                                </svg>
                                                    }
                                                </div>
                                                <div className="thu items-center text-center w-full" >
                                                    <p className={clsx(
                                                        "font-normal",
                                                        screenSize?.lte960 ? "text-sm leading-sm" : "text-md leading-md",
                                                        value.isJoin === null ? (value.isActive ? 'text-brand-600' : 'text-quaternary-500') :
                                                            value.isJoin ? 'text-utility-orange-dark-400' : 'text-quaternary-500'
                                                    )}>{value.name}</p>
                                                </div>
                                            </div>
                                        })
                                    }

                                </div>
                                <div className="text_mo_ta w-full text-center">
                                    {
                                        isFinish ?
                                            <p className={clsx("text-tertiary-600 font-normal",screenSize?.lte960 ? " text-sm leading-sm" : " text-md leading-md")}>Streak dài nhất: <strong>{dataFinish?.max_streak}</strong></p>
                                            :
                                            <p className={clsx("text-tertiary-600 font-normal",screenSize?.lte960 ? " text-sm leading-sm" : " text-md leading-md")}>Nhấn <strong>“Bắt đầu”</strong> liền đi tụ bây!</p>
                                    }

                                </div>
                            </div>
                            {
                                !isFinish ?
                                    <>
                                        <div className="summary_streak mx-auto h-[170px] max-w-[420px] w-full flex flex-wrap  rounded-[8px] border border-[#F5F5F5] justify-center">
                                            <div className="items_summary w-1/2 h-1/2 border border-[#F5F5F5] flex flex-col justify-center items-center text-center">
                                                <p className={clsx(
                                                    "title_item_summary font-semibold text-utility-brand-600",
                                                    screenSize?.lte960 ? "text-sm leading-sm" : "text-md leading-md"
                                                )}>Tổng số câu đúng</p>
                                                <p className={clsx(
                                                    "number_item_summary font-semibold text-secondary-700",
                                                    screenSize?.lte960 ? "text-md leading-md" : "text-lg leading-lg"
                                                )}>{streakData.trueCount ? streakData.trueCount : '--'}</p>
                                            </div>
                                            <div className="items_summary w-1/2 h-1/2 border border-[#F5F5F5] flex flex-col justify-center items-center text-center">
                                                <p className={clsx(
                                                    "title_item_summary font-semibold text-utility-brand-600",
                                                    screenSize?.lte960 ? "text-sm leading-sm" : "text-md leading-md"
                                                )}>Tỉ lệ đúng</p>
                                                <p className={clsx(
                                                    "number_item_summary font-semibold text-secondary-700",
                                                    screenSize?.lte960 ? "text-md leading-md" : "text-lg leading-lg"
                                                )}>{ (!streakData.trueCount || !streakData.totalCount) ? '--' : Math.round(streakData.trueCount *100 / streakData.totalCount) + '%'}</p>
                                            </div>
                                            <div className="items_summary w-1/2 h-1/2 border border-[#F5F5F5] flex flex-col justify-center items-center text-center">
                                                <p className={clsx(
                                                    "title_item_summary font-semibold text-utility-brand-600",
                                                    screenSize?.lte960 ? "text-sm leading-sm" : "text-md leading-md"
                                                )}>Thời gian trung bình</p>
                                                <p className={clsx(
                                                    "number_item_summary font-semibold text-secondary-700",
                                                    screenSize?.lte960 ? "text-md leading-md" : "text-lg leading-lg"
                                                )}>
                                                    ⏱️ <strong className="mr-xs"></strong>
                                                    {streakData.timeCount ? convertSecond2Text(streakData.timeCount) : '--'}
                                                </p>
                                            </div>
                                            <div className="items_summary w-1/2 h-1/2 border border-[#F5F5F5] flex flex-col justify-center items-center text-center">
                                                <p className={clsx(
                                                    "title_item_summary font-semibold text-utility-brand-600",
                                                    screenSize?.lte960 ? "text-sm leading-sm" : "text-md leading-md"
                                                )}>Xếp hạng</p>
                                                <p className={clsx(
                                                    "number_item_summary font-semibold text-secondary-700",
                                                    screenSize?.lte960 ? "text-md leading-md" : "text-lg leading-lg"
                                                )}>
                                                    🏆 <strong className="mr-xs"></strong>
                                                    {streakData.rankingCount ? streakData.rankingCount : '--'}
                                                </p>
                                            </div>
                                        </div>
                                    </>
                                    :
                                    <>
                                        <div className=" flex flex-col gap-y-3xl mt-3xl justify-center w-full">
                                            <div className="flex flex-row justify-center items-center gap-xs">
                                                <p className="text-utility-brand-600 text-md leading-md font-semibold">Điểm nhận được</p>
                                                <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 21 21" fill="none">
                                                    <g clipPath="url(#clip0_5409_12476)">
                                                        <path fillRule="evenodd" clipRule="evenodd" d="M9.53709 1.82439C10.1325 1.48302 10.8662 1.48302 11.4616 1.82439L17.8704 5.49809C18.4659 5.83946 18.8327 6.4707 18.8327 7.15345V14.5018C18.8327 15.1845 18.4658 15.8158 17.8704 16.1571L11.4616 19.8308C10.8662 20.1722 10.1325 20.1722 9.53709 19.8308L3.12828 16.1571C2.53285 15.8158 2.16604 15.1845 2.16602 14.5018V7.15345C2.16602 6.4707 2.53283 5.83946 3.12828 5.49809L9.53709 1.82439ZM10.7822 5.3012C10.6962 5.02533 10.3025 5.02533 10.2165 5.3012L9.67992 7.02841C9.23655 8.45603 8.11148 9.57411 6.6738 10.0144L4.9344 10.5463C4.65616 10.6315 4.65627 11.0227 4.9344 11.108L6.6738 11.6408C8.11144 12.0811 9.23656 13.1992 9.67992 14.6268L10.2165 16.3531C10.3023 16.6294 10.6964 16.6294 10.7822 16.3531L11.3188 14.6268C11.7621 13.1992 12.8873 12.0811 14.3249 11.6408L16.0643 11.108C16.3424 11.0227 16.3425 10.6315 16.0643 10.5463L14.3249 10.0144C12.8872 9.57411 11.7621 8.45603 11.3188 7.02841L10.7822 5.3012Z" fill="#7A5AF8"/>
                                                    </g>
                                                    <defs>
                                                        <clipPath id="clip0_5409_12476">
                                                            <rect width="20" height="20" fill="white" transform="translate(0.5 0.828125)"/>
                                                        </clipPath>
                                                    </defs>
                                                </svg>
                                                <p className="font-semibold text-lg leading-lg text-secondary-700"> + {dataFinish?.point}</p>
                                            </div>
                                            <div className="summary_streak mx-auto h-[170px] max-w-[420px] w-full flex flex-wrap  rounded-[8px] border border-[#F5F5F5] justify-center">
                                                <div className="items_summary w-1/2 h-1/2 border border-[#F5F5F5] flex flex-col justify-center items-center text-center">
                                                    <p className={clsx("title_item_summary font-semibold text-utility-brand-600",screenSize?.lte960 ? "text-sm leading-sm":"text-md leading-md")}>Số câu đúng</p>
                                                    <p className={clsx("number_item_summary font-semibold text-secondary-700",screenSize?.lte960 ? "text-md leading-md" : "text-lg leading-lg")}>{dataFinish?.is_correct}</p>
                                                </div>
                                                <div className="items_summary w-1/2 h-1/2 border border-[#F5F5F5] flex flex-col justify-center items-center text-center">
                                                    <p className={clsx("title_item_summary font-semibold text-utility-brand-600",screenSize?.lte960 ? "text-sm leading-sm":"text-md leading-md")}>Thời gian làm bài</p>
                                                    <p className={clsx("number_item_summary font-semibold text-secondary-700 flex flex-row items-center",screenSize?.lte960 ? "text-md leading-md" : "text-lg leading-lg")}>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                                                            <path d="M10.4993 18.3327C15.1017 18.3327 18.8327 14.6017 18.8327 9.99935C18.8327 5.39698 15.1017 1.66602 10.4993 1.66602C5.89698 1.66602 2.16602 5.39698 2.16602 9.99935C2.16602 14.6017 5.89698 18.3327 10.4993 18.3327Z" fill="#4294FF"/>
                                                            <path d="M10.4993 16.5888C14.1384 16.5888 17.0885 13.6387 17.0885 9.99964C17.0885 6.36056 14.1384 3.41049 10.4993 3.41049C6.86026 3.41049 3.9102 6.36056 3.9102 9.99964C3.9102 13.6387 6.86026 16.5888 10.4993 16.5888Z" fill="#EEF3FF"/>
                                                            <path d="M12.8249 12.906C12.7486 12.9061 12.6729 12.8911 12.6024 12.8619C12.5319 12.8326 12.4678 12.7898 12.4139 12.7357L10.0883 10.4101C9.97927 10.3011 9.91801 10.1532 9.91796 9.99904L9.91796 6.12307C9.91796 5.96887 9.97921 5.82099 10.0882 5.71196C10.1973 5.60293 10.3452 5.54167 10.4994 5.54167C10.6536 5.54167 10.8014 5.60293 10.9105 5.71196C11.0195 5.82099 11.0807 5.96887 11.0807 6.12307V9.7583L13.236 11.9136C13.3173 11.9949 13.3726 12.0985 13.3951 12.2112C13.4175 12.324 13.406 12.4409 13.362 12.5471C13.318 12.6533 13.2435 12.7441 13.1479 12.808C13.0523 12.8719 12.9399 12.906 12.8249 12.906Z" fill="#4294FF"/>
                                                        </svg>
                                                        <strong className="mr-xs"></strong>
                                                        {convertSecond2Text(dataFinish?.time)}
                                                    </p>
                                                </div>
                                                <div className="items_summary w-full h-1/2 border border-[#F5F5F5] flex flex-col justify-center items-center text-center">
                                                    <p className={clsx("title_item_summary font-semibold text-utility-brand-600",screenSize?.lte960 ? "text-sm leading-sm":"text-md leading-md")}>Xếp hạng</p>
                                                    <p className={clsx("number_item_summary font-semibold text-secondary-700",screenSize?.lte960 ? "text-md leading-md" : "text-lg leading-lg")}>
                                                        🏆 <strong className="mr-xs"></strong>
                                                        {streakData.rankingCount ? streakData.rankingCount : '--'}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </>
                            }
                            {
                                !isFinish ?
                                    <div className= "button_tham_gia w-full max-w-[420px] h-[48px] rounded-md border border-disabled_subtle">
                                        <button className={clsx(
                                            "w-full h-full py-lg px-[18px] rounded-md",
                                            dayActive?.documentId ? 'text-[#A4A7AE]' : 'text-white bg-[#299D55]'
                                        )} onClick={clickStart}
                                        >
                                            <p className="text-md leading-md font-semibold">{dayActive?.documentId ? 'Đã tham gia' : 'Bắt đầu'}</p>
                                        </button>
                                    </div>
                                    :
                                    <div className="button_tham_gia h-[48px] w-full max-w-[420px] rounded-md border border-disabled_subtle bg-[#299D55]">
                                        <button className="w-full h-full py-lg px-[18px]" onClick={closeStreakHomePage}>
                                            <p className=" text-md leading-md font-semibold text-white">Quay về trang chủ</p>
                                        </button>
                                    </div>
                            }
                        </div>
                    </div>
                </div>
                {
                    isViewTutorial ?  <TutorialStreak
                        isOpen={isViewTutorial}
                        onClose={() => {setIsViewTutorial(false)}}
                        onSendData={(data) => {debugger
                        }}
                    ></TutorialStreak> : ''
                }
            </>
        );
    }else {
        return (
            <>
                <div className="flex flex-col items-center justify-center h-full min-h-[calc(100vh-160px)] absolute top-0 left-0 bg-white w-full"
                     style={{zIndex: 100}}>
                    <div className="w-full flex flex-col fixed top-0 left-0 items-center bg-white" style={{zIndex:110}}>
                        <div className="header_streak  max-w-[1200px] w-full h-[76px] p-xl text-center">
                            <div className="w-full flex flex-row justify-between h-[44px]">
                                <div className="close_streak p-[8px] w-[36px] h-full cursor-pointer" onClick={closeStreak}>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <path d="M18 6L6 18M6 6L18 18" stroke="#A4A7AE" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                </div>
                                <div className="items_summary w-1/2 h-1/2 flex flex-row justify-center items-center text-center">
                                    <div>
                                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                                            <path d="M10.5003 7.91663V11.25L12.5837 12.5M10.5003 4.16663C6.58831 4.16663 3.41699 7.33794 3.41699 11.25C3.41699 15.162 6.58831 18.3333 10.5003 18.3333C14.4123 18.3333 17.5837 15.162 17.5837 11.25C17.5837 7.33794 14.4123 4.16663 10.5003 4.16663ZM10.5003 4.16663V1.66663M8.83366 1.66663H12.167M17.4411 4.65999L16.1911 3.40999L16.8161 4.03499M3.55951 4.65999L4.80951 3.40999L4.18451 4.03499" stroke="#535862" strokeWidth="1.67" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    </div>
                                    <p className={clsx("number_item_summary font-semibold text-secondary-700 ml-xs",screenSize?.lte960 ? "text-sm leading-sm": "text-lg leading-lg")}>
                                        {time ? convertSecond2Text(time) : '--'}
                                    </p>
                                </div>
                                <div className="tooltip_streak h-full w-[44px] p-lg cursor-pointer" onClick={reportStreak}>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                        <path d="M3.33301 12.5C3.33301 12.5 4.16634 11.6666 6.66634 11.6666C9.16634 11.6666 10.833 13.3333 13.333 13.3333C15.833 13.3333 16.6663 12.5 16.6663 12.5V2.49996C16.6663 2.49996 15.833 3.33329 13.333 3.33329C10.833 3.33329 9.16634 1.66663 6.66634 1.66663C4.16634 1.66663 3.33301 2.49996 3.33301 2.49996L3.33301 18.3333" stroke="#535862" strokeWidth="1.66667" strokeLinecap="round" strokeLinejoin="round"/>
                                    </svg>
                                </div>
                            </div>
                            <div className="progess_bar rounded-full max-w-[1200px] w-full h-[8px] bg-[#E9EAEB] text-left">
                                <div className="h-[8px] bg-[#299D55] rounded-full"
                                style={{width: lengthProgress + "%"}}
                                ></div>
                            </div>
                        </div>

                    </div>
                    {
                        !isQuestion2 ?
                            //Câu hỏi và câu trả lời
                            <>
                                <div className="container_streak w-full py-3xl px-xl flex flex-col items-center overflow-auto h-fit pt-[76px] pb-[150px]">
                                <div className="max-w-[622px] w-full flex flex-col items-start">
                                    <div className="number_title flex flex-row h-[28px] gap-[8px] text-left mb-sm">
                                        <p className="font-semibold text-lg leading-lg text-primary-900">Câu {(indexQuestionActive + 1 )}</p>
                                        <div className="h-full flex flex-row gap-xs py-xs pl-md pr-[10px] items-center bg-utility-brand-75 rounded-md">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                                <path fillRule="evenodd" clipRule="evenodd" d="M9.18463 1.00092C9.68153 0.987068 10.1716 1.11002 10.5308 1.46918C10.89 1.82837 11.0129 2.31847 10.9991 2.81537C10.9853 3.31086 10.8363 3.86113 10.5948 4.42474C10.3795 4.92707 10.0815 5.45911 9.71344 5.99994C10.0815 6.54075 10.3795 7.07282 10.5948 7.57514C10.8363 8.13874 10.9852 8.68903 10.9991 9.18451C11.0129 9.68143 10.89 10.1715 10.5308 10.5307C10.1716 10.8899 9.68156 11.0128 9.18463 10.999C8.68915 10.9851 8.13886 10.8362 7.57526 10.5947C7.07295 10.3794 6.54087 10.0814 6.00006 9.71332C5.45923 10.0814 4.9272 10.3794 4.42487 10.5947C3.86125 10.8362 3.31098 10.9851 2.81549 10.999C2.31859 11.0128 1.82849 10.8899 1.4693 10.5307C1.11014 10.1715 0.98719 9.68141 1.00104 9.18451C1.01488 8.68903 1.16379 8.13874 1.40534 7.57514C1.62059 7.07288 1.91822 6.54068 2.2862 5.99994C1.91823 5.45921 1.62058 4.92699 1.40534 4.42474C1.1638 3.86114 1.01486 3.31084 1.00104 2.81537C0.987202 2.31847 1.11011 1.82837 1.4693 1.46918C1.82849 1.10999 2.31859 0.98708 2.81549 1.00092C3.31096 1.01474 3.86126 1.16368 4.42487 1.40521C4.92711 1.62046 5.45933 1.91811 6.00006 2.28607C6.54081 1.91809 7.073 1.62047 7.57526 1.40521C8.13886 1.16367 8.68915 1.01475 9.18463 1.00092ZM2.92145 6.85346C2.6754 7.24222 2.47527 7.61801 2.32477 7.96918C2.11281 8.46375 2.00969 8.88533 2.00055 9.21234C1.99147 9.53782 2.0747 9.72102 2.17682 9.82318C2.27896 9.92532 2.46215 10.0085 2.78766 9.99945C3.11467 9.99033 3.53624 9.88719 4.03082 9.67523C4.38196 9.52475 4.75734 9.32408 5.14606 9.07806C4.75171 8.75943 4.35842 8.40713 3.97565 8.02435C3.5927 7.64141 3.2402 7.24799 2.92145 6.85346ZM9.07819 6.85346C8.75947 7.24793 8.40737 7.64146 8.02448 8.02435C7.64158 8.40725 7.24805 8.75935 6.85358 9.07806C7.2424 9.32416 7.61808 9.5247 7.9693 9.67523C8.46387 9.88719 8.88546 9.99031 9.21246 9.99945C9.53801 10.0085 9.72116 9.92533 9.8233 9.82318C9.92545 9.72104 10.0087 9.53789 9.99957 9.21234C9.99044 8.88533 9.88731 8.46375 9.67535 7.96918C9.52483 7.61796 9.32429 7.24228 9.07819 6.85346ZM2.78766 2.00043C2.46215 1.99136 2.27896 2.07456 2.17682 2.1767C2.07468 2.27884 1.99149 2.46203 2.00055 2.78754C2.00968 3.11454 2.11282 3.53613 2.32477 4.0307C2.47522 4.38175 2.67552 4.75731 2.92145 5.14594C3.24015 4.75148 3.59278 4.3584 3.97565 3.97553C4.35852 3.59266 4.7516 3.24003 5.14606 2.92133C4.75744 2.6754 4.38187 2.4751 4.03082 2.32465C3.53625 2.1127 3.11466 2.00955 2.78766 2.00043ZM9.21246 2.00043C8.88546 2.00956 8.46387 2.11269 7.9693 2.32465C7.61813 2.47515 7.24234 2.67528 6.85358 2.92133C7.24811 3.24008 7.64153 3.59258 8.02448 3.97553C8.40725 4.3583 8.75955 4.75158 9.07819 5.14594C9.3242 4.75722 9.52487 4.38183 9.67535 4.0307C9.88732 3.53611 9.99045 3.11455 9.99957 2.78754C10.0086 2.46203 9.92545 2.27884 9.8233 2.1767C9.72114 2.07458 9.53795 1.99135 9.21246 2.00043Z" fill="#7A5AF8"/>
                                                <circle cx="6" cy="6" r="1" fill="#EBE9FE"/>
                                            </svg>
                                            <p className="font-medium text-sm leading-sm text-utility-brand-700">{questionActive?.exercise_type.point}</p>
                                        </div>
                                    </div>
                                    <div className={clsx("content_cau_hoi text-primary-900 font-normal mb-lg")}>
                                        <KaTeXRenderer content={questionActive?.content} className={clsx(screenSize?.lte960 ? "text-sm leading-sm": "text-md leading-md")}></KaTeXRenderer>
                                    </div>
                                    {
                                        questionActive?.image_path ?
                                            <div className="image_cau_hoi w-full aspect-[622.00/247.83] border border-secondary relative">
                                                <img src={questionActive?.image_path} className="w-full h-full object-cover no-repeat"></img>
                                                <div className="absolute top-[6px] right-[6px] rounded-md items-center border border-[#D5D7DA] p-md cursor-pointer" onClick={viewFullImage}>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                                        <path d="M17.5 11.6667V13.5C17.5 14.9001 17.5 15.6002 17.2275 16.135C16.9878 16.6054 16.6054 16.9878 16.135 17.2275C15.6002 17.5 14.9001 17.5 13.5 17.5H11.6667M8.33333 2.5H6.5C5.09987 2.5 4.3998 2.5 3.86502 2.77248C3.39462 3.01217 3.01217 3.39462 2.77248 3.86502C2.5 4.3998 2.5 5.09987 2.5 6.5V8.33333M12.5 7.5L17.5 2.5M17.5 2.5H12.5M17.5 2.5V7.5M7.5 12.5L2.5 17.5M2.5 17.5H7.5M2.5 17.5L2.5 12.5" stroke="#414651" strokeWidth="1.66667" strokeLinecap="round" strokeLinejoin="round"/>
                                                    </svg>
                                                </div>
                                            </div>
                                            : ""
                                    }
                                    <div className={clsx(
                                        "cau_tra_loi flex flex-col gap-y-lg mt-4xl w-full",
                                        isViewAnswer ? "pointer-events-none" : ""
                                    )}>
                                        <div className={clsx(
                                            "cau_tl_items flex flex-row p-lg items-center gap-[8px] border w-full cursor-pointer rounded-md border-tertiary bg-white",
                                            questionActive?.A ?  '' : 'hidden',
                                            (userAnswer?.answer === "A" && isViewAnswer && !userAnswer.is_correct) &&  '!border-error !bg-error-primary' ,
                                            (questionActive?.correct_answer_type === "A" && isViewAnswer) &&  '!border-[#45BF76] !bg-success-primary',
                                        )} onClick={() => {handelUserAnswer("A")}}>
                                            <div className={clsx(
                                                "w-[24px] aspect-square rounded-full flex justify-center items-center font-normal text-sm leading-sm bg-utility-gray-100 text-primary-900",
                                                userAnswer?.answer === "A" && '!bg-brand-solid !text-white',
                                                (userAnswer?.answer === "A" && isViewAnswer && !userAnswer.is_correct) &&  '!bg-error-solid' ,
                                                (questionActive?.correct_answer_type === "A" && isViewAnswer) &&  '!bg-brand-solid !text-white',
                                                screenSize?.lte960 ? "":""
                                            )}>
                                                A
                                            </div>
                                            <div className="text-primary-900 !text-md !leading-md font-normal flex-1">
                                                <KaTeXRenderer content={questionActive?.A} className={clsx(screenSize?.lte960 ? "!text-sm !leading-sm": "text-md leading-md")}></KaTeXRenderer>
                                            </div>
                                        </div>
                                        <div className={clsx(
                                            "cau_tl_items flex flex-row p-lg items-center gap-[8px] border w-full cursor-pointer rounded-md border-tertiary bg-white",
                                            questionActive?.B ?  '' : 'hidden',
                                            (userAnswer?.answer === "B" && isViewAnswer && !userAnswer.is_correct) &&  '!border-error !bg-error-primary' ,
                                            (questionActive?.correct_answer_type === "B" && isViewAnswer) &&  '!border-[#45BF76] !bg-success-primary',

                                        )} onClick={() => {handelUserAnswer("B")}}>
                                            <div className={clsx(
                                                "w-[24px] aspect-square rounded-full flex justify-center items-center font-normal text-sm leading-none bg-utility-gray-100 text-primary-900",
                                                userAnswer?.answer === "B" && '!bg-brand-solid !text-white',
                                                (userAnswer?.answer === "B" && isViewAnswer && !userAnswer.is_correct) &&  '!bg-error-solid' ,
                                                (questionActive?.correct_answer_type === "B" && isViewAnswer) &&  '!bg-brand-solid !text-white',
                                            )}>
                                                B
                                            </div>
                                            <div className="text-primary-900 text-md leading-md font-normal flex-1">
                                                <KaTeXRenderer content={questionActive?.B} className={clsx(screenSize?.lte960 ? "text-sm leading-sm": "text-md leading-md")} ></KaTeXRenderer>
                                            </div>
                                        </div>
                                        <div className={clsx(
                                            "cau_tl_items flex flex-row p-lg items-center gap-[8px] border w-full cursor-pointer rounded-md border-tertiary bg-white",
                                            questionActive?.C ?  '' : 'hidden',
                                            (userAnswer?.answer === "C" && isViewAnswer && !userAnswer.is_correct) &&  '!border-error !bg-error-primary' ,
                                            (questionActive?.correct_answer_type === "C" && isViewAnswer) &&  '!border-[#45BF76] !bg-success-primary',
                                        )} onClick={() => {handelUserAnswer("C")}}>
                                            <div className={clsx(
                                                "w-[24px] aspect-square rounded-full flex justify-center items-center font-normal text-sm leading-none bg-utility-gray-100 text-primary-900",
                                                userAnswer?.answer === "C" && '!bg-brand-solid !text-white',
                                                (userAnswer?.answer === "C" && isViewAnswer && !userAnswer.is_correct) &&  '!bg-error-solid' ,
                                                (questionActive?.correct_answer_type === "C" && isViewAnswer) &&  '!bg-brand-solid !text-white',
                                            )}>
                                                C
                                            </div>
                                            <div className="text-primary-900 text-md leading-md font-normal flex-1">
                                                <KaTeXRenderer content={questionActive?.C} className={clsx(screenSize?.lte960 ? "text-sm leading-sm": "text-md leading-md")}></KaTeXRenderer>
                                            </div>
                                        </div>
                                        <div className={clsx(
                                            "cau_tl_items flex flex-row p-lg items-center gap-[8px] border w-full cursor-pointer rounded-md border-tertiary bg-white",
                                            questionActive?.D ?  '' : 'hidden',
                                            (userAnswer?.answer === "D" && isViewAnswer && !userAnswer.is_correct) &&  '!border-error !bg-error-primary' ,
                                            (questionActive?.correct_answer_type === "D" && isViewAnswer) &&  '!border-[#45BF76] !bg-success-primary',
                                        )} onClick={() => {handelUserAnswer("D")}}>
                                            <div className={clsx(
                                                "w-[24px] aspect-square rounded-full flex justify-center items-center font-normal text-sm leading-none bg-utility-gray-100 text-primary-900",
                                                userAnswer?.answer === "D" && '!bg-brand-solid !text-white',
                                                (userAnswer?.answer === "D" && isViewAnswer && !userAnswer.is_correct) &&  '!bg-error-solid' ,
                                                (questionActive?.correct_answer_type === "D" && isViewAnswer) &&  '!bg-brand-solid !text-white',
                                            )}>
                                                D
                                            </div>
                                            <div className="text-primary-900 text-md leading-md font-normal flex-1">
                                                <KaTeXRenderer content={questionActive?.D} className={clsx(screenSize?.lte960 ? "text-sm leading-sm": "text-md leading-md")}></KaTeXRenderer>
                                            </div>
                                        </div>
                                    </div>
                                    <div className={clsx(
                                        "giai_thich gap-y-sm flex flex-col mt-4xl",
                                        isViewAnswer ? "" : "hidden"
                                    )}>
                                        <div className="dap_an flex flex-row gap-sm">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                                <g clipPath="url(#clip0_5304_13066)">
                                                    <path d="M6.24935 9.99935L8.74935 12.4993L13.7493 7.49935M18.3327 9.99935C18.3327 14.6017 14.6017 18.3327 9.99935 18.3327C5.39698 18.3327 1.66602 14.6017 1.66602 9.99935C1.66602 5.39698 5.39698 1.66602 9.99935 1.66602C14.6017 1.66602 18.3327 5.39698 18.3327 9.99935Z" stroke="#299D55" strokeWidth="1.67" strokeLinecap="round" strokeLinejoin="round"/>
                                                </g>
                                                <defs>
                                                    <clipPath id="clip0_5304_13066">
                                                        <rect width="20" height="20" fill="white"/>
                                                    </clipPath>
                                                </defs>
                                            </svg>
                                            <p className={clsx("text-utility-brand-600 font-semibold",screenSize?.lte960 ? "text-sm leading-sm":"text-md leading-md")}>Đáp án đúng {questionActive?.correct_answer_type}</p>
                                        </div>
                                        <div className="explain_ font-normal text-md leading-md text-secondary-700">
                                            <KaTeXRenderer content={questionActive?.explain}
                                                           className={clsx(screenSize?.lte960 ? "text-sm leading-sm": "text-md leading-md")}>
                                            </KaTeXRenderer>
                                        </div>
                                    </div>
                                </div>
                            </div>
                                <div className="footer_streak w-full h-fit py-3xl px-xl fixed bg-white z-50 bottom-0 left-0 border-t border-primary">
                                    <div className={clsx(
                                        "diem_ mb-xl flex flex-row gap-md justify-center items-center w-full",
                                        isViewAnswer ? "" : "hidden"
                                    )}>
                                        {
                                            userAnswer.is_correct ?
                                                <>
                                                    <p className={clsx("text-tertiary-600 font-normal",screenSize?.lte960? "text-sm leading-sm":"text-md leading-md")}>🎉 Bạn đã đạt</p>
                                                    <div className="flex flex-row gap-xs justify-center items-center rounded-md bg-utility-brand-75 py-xs px-[10px]" >
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 21 21" fill="none">
                                                            <g clipPath="url(#clip0_5409_12476)">
                                                                <path fillRule="evenodd" clipRule="evenodd" d="M9.53709 1.82439C10.1325 1.48302 10.8662 1.48302 11.4616 1.82439L17.8704 5.49809C18.4659 5.83946 18.8327 6.4707 18.8327 7.15345V14.5018C18.8327 15.1845 18.4658 15.8158 17.8704 16.1571L11.4616 19.8308C10.8662 20.1722 10.1325 20.1722 9.53709 19.8308L3.12828 16.1571C2.53285 15.8158 2.16604 15.1845 2.16602 14.5018V7.15345C2.16602 6.4707 2.53283 5.83946 3.12828 5.49809L9.53709 1.82439ZM10.7822 5.3012C10.6962 5.02533 10.3025 5.02533 10.2165 5.3012L9.67992 7.02841C9.23655 8.45603 8.11148 9.57411 6.6738 10.0144L4.9344 10.5463C4.65616 10.6315 4.65627 11.0227 4.9344 11.108L6.6738 11.6408C8.11144 12.0811 9.23656 13.1992 9.67992 14.6268L10.2165 16.3531C10.3023 16.6294 10.6964 16.6294 10.7822 16.3531L11.3188 14.6268C11.7621 13.1992 12.8873 12.0811 14.3249 11.6408L16.0643 11.108C16.3424 11.0227 16.3425 10.6315 16.0643 10.5463L14.3249 10.0144C12.8872 9.57411 11.7621 8.45603 11.3188 7.02841L10.7822 5.3012Z" fill="#7A5AF8"/>
                                                            </g>
                                                            <defs>
                                                                <clipPath id="clip0_5409_12476">
                                                                    <rect width="20" height="20" fill="white" transform="translate(0.5 0.828125)"/>
                                                                </clipPath>
                                                            </defs>
                                                        </svg>
                                                        <p className="font-medium text-sm leading-sm text-utility-brand-700"> + {userAnswer?.is_star_point ? questionActive?.exercise_type.point * 2 : questionActive?.exercise_type.point}</p>
                                                    </div>
                                                </>
                                                :
                                                <p className="text-tertiary-600 text-md leading-md font-normal">Tiếc quá, cố gắng thêm xíu nè 💪</p>
                                        }

                                    </div>
                                    <div className="items-center justify-center flex flex-row gap-x-[16px]">
                                        {
                                            !isUseStarPoint ?
                                                userAnswer.is_star_point ?
                                                    <button onClick={useStarPoint} className={clsx(
                                                        "sao_hi_vong py-lg px-[18px] rounded-md border border-[#B42318] bg-[#DA251D] ",
                                                        " text-md leading-md font-semibold text-[#FFF600] text-center flex flex-row items-center justify-center gap-sm",
                                                        screenSize?.bw375640 ? "":"max-w-[215px] w-full "
                                                    )}>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                                                            <path d="M9.40253 2.8778C9.59461 2.48869 9.69064 2.29413 9.82102 2.23197C9.93445 2.17789 10.0662 2.17789 10.1797 2.23197C10.31 2.29413 10.4061 2.48869 10.5981 2.8778L12.4204 6.56944C12.4771 6.68432 12.5054 6.74176 12.5469 6.78635C12.5836 6.82584 12.6276 6.85783 12.6764 6.88056C12.7316 6.90623 12.795 6.91549 12.9218 6.93402L16.9978 7.5298C17.427 7.59253 17.6417 7.6239 17.741 7.72874C17.8274 7.81995 17.868 7.94529 17.8516 8.06985C17.8327 8.21302 17.6773 8.36436 17.3666 8.66702L14.4182 11.5387C14.3263 11.6282 14.2803 11.673 14.2507 11.7263C14.2244 11.7734 14.2076 11.8252 14.2011 11.8788C14.1937 11.9393 14.2046 12.0025 14.2263 12.129L14.922 16.1851C14.9953 16.6129 15.032 16.8269 14.9631 16.9538C14.9031 17.0642 14.7965 17.1417 14.6729 17.1646C14.5308 17.1909 14.3388 17.0899 13.9546 16.8879L10.3106 14.9716C10.1971 14.9119 10.1403 14.882 10.0805 14.8703C10.0276 14.8599 9.97311 14.8599 9.92015 14.8703C9.86034 14.882 9.80358 14.9119 9.69004 14.9716L6.0461 16.8879C5.66192 17.0899 5.46984 17.1909 5.3278 17.1646C5.20423 17.1417 5.09759 17.0642 5.03761 16.9538C4.96866 16.8269 5.00535 16.6129 5.07872 16.1851L5.7744 12.129C5.79609 12.0025 5.80693 11.9393 5.79959 11.8788C5.7931 11.8252 5.77625 11.7734 5.75 11.7263C5.72034 11.673 5.67439 11.6282 5.58248 11.5387L2.63412 8.66702C2.32338 8.36436 2.168 8.21302 2.1491 8.06985C2.13265 7.94529 2.17329 7.81995 2.2597 7.72874C2.35902 7.6239 2.57363 7.59253 3.00286 7.5298L7.07892 6.93402C7.20568 6.91549 7.26906 6.90623 7.32426 6.88056C7.37313 6.85783 7.41713 6.82584 7.45381 6.78635C7.49525 6.74176 7.5236 6.68432 7.5803 6.56944L9.40253 2.8778Z" fill="#FFF700" stroke="#FFF700" strokeWidth="1.66667" strokeLinecap="round" strokeLinejoin="round"/>
                                                        </svg>
                                                        {screenSize?.bw375640 ? "":"Đã đặt sao hy vọng"}

                                                    </button>
                                                    :
                                                    <button onClick={useStarPoint} className={clsx(
                                                        "sao_hi_vong  py-lg px-[18px] rounded-md border border-[#8EE5BA] text-md leading-md font-semibold text-[#198C43] text-center flex flex-row items-center justify-center gap-sm"
                                                        ,isViewAnswer ? "pointer-events-none" : "",
                                                        screenSize?.bw375640 ? "":"max-w-[215px] w-full "
                                                    )}>
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                                                            <path d="M9.90253 2.8778C10.0946 2.48869 10.1906 2.29413 10.321 2.23197C10.4344 2.17789 10.5662 2.17789 10.6797 2.23197C10.81 2.29413 10.9061 2.48869 11.0981 2.8778L12.9204 6.56944C12.9771 6.68432 13.0054 6.74176 13.0469 6.78635C13.0836 6.82584 13.1276 6.85783 13.1764 6.88056C13.2316 6.90623 13.295 6.91549 13.4218 6.93402L17.4978 7.5298C17.927 7.59253 18.1417 7.6239 18.241 7.72874C18.3274 7.81995 18.368 7.94529 18.3516 8.06985C18.3327 8.21302 18.1773 8.36436 17.8666 8.66702L14.9182 11.5387C14.8263 11.6282 14.7803 11.673 14.7507 11.7263C14.7244 11.7734 14.7076 11.8252 14.7011 11.8788C14.6937 11.9393 14.7046 12.0025 14.7263 12.129L15.422 16.1851C15.4953 16.6129 15.532 16.8269 15.4631 16.9538C15.4031 17.0642 15.2965 17.1417 15.1729 17.1646C15.0308 17.1909 14.8388 17.0899 14.4546 16.8879L10.8106 14.9716C10.6971 14.9119 10.6403 14.882 10.5805 14.8703C10.5276 14.8599 10.4731 14.8599 10.4202 14.8703C10.3603 14.882 10.3036 14.9119 10.19 14.9716L6.5461 16.8879C6.16192 17.0899 5.96984 17.1909 5.8278 17.1646C5.70423 17.1417 5.59759 17.0642 5.53761 16.9538C5.46866 16.8269 5.50535 16.6129 5.57872 16.1851L6.2744 12.129C6.29609 12.0025 6.30693 11.9393 6.29959 11.8788C6.2931 11.8252 6.27625 11.7734 6.25 11.7263C6.22034 11.673 6.17439 11.6282 6.08248 11.5387L3.13413 8.66702C2.82338 8.36436 2.668 8.21302 2.6491 8.06985C2.63265 7.94529 2.67329 7.81995 2.7597 7.72874C2.85902 7.6239 3.07363 7.59253 3.50286 7.5298L7.57892 6.93402C7.70568 6.91549 7.76906 6.90623 7.82426 6.88056C7.87313 6.85783 7.91713 6.82584 7.95381 6.78635C7.99525 6.74176 8.0236 6.68432 8.0803 6.56944L9.90253 2.8778Z" stroke="#198C43" strokeWidth="1.66667" strokeLinecap="round" strokeLinejoin="round"/>
                                                        </svg>
                                                        {screenSize?.bw375640 ? "":" Đặt sao hy vọng"}

                                                    </button>
                                                :
                                                <button className={clsx(
                                                    "sao_hi_vong py-lg px-[18px] rounded-md border border-[#E9EAEB]",
                                                    "text-center flex flex-row items-center justify-center gap-sm",
                                                    screenSize?.bw375640 ? "":"max-w-[215px] w-full "
                                                )}>
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                                                        <path d="M15.4993 11.0333L17.8656 8.66702C18.1763 8.36436 18.3317 8.21302 18.3506 8.06985C18.3671 7.94529 18.3264 7.81995 18.24 7.72874C18.1407 7.6239 17.9261 7.59253 17.4968 7.5298L13.4208 6.93402C13.294 6.91549 13.2306 6.90623 13.1754 6.88056C13.1266 6.85783 13.0826 6.82584 13.0459 6.78635C13.0045 6.74176 12.9761 6.68432 12.9194 6.56944L11.0972 2.8778C10.9051 2.48869 10.8091 2.29413 10.6787 2.23197C10.5653 2.17789 10.4335 2.17789 10.32 2.23197C10.1897 2.29413 10.0936 2.48869 9.90156 2.8778L9.21425 4.24257M15.1719 14.8703L15.421 16.1851C15.4944 16.6129 15.531 16.8269 15.4621 16.9538C15.4021 17.0642 15.2955 17.1417 15.1719 17.1646C15.0299 17.1909 14.8378 17.0899 14.4536 16.8879L10.8097 14.9716C10.6961 14.9119 10.6394 14.882 10.5796 14.8703C10.5266 14.8599 10.4721 14.8599 10.4192 14.8703C10.3594 14.882 10.3026 14.9119 10.1891 14.9716L6.54512 16.8879C6.16095 17.0899 5.96886 17.1909 5.82683 17.1646C5.70325 17.1417 5.59662 17.0642 5.53663 16.9538C5.46768 16.8269 5.50437 16.6129 5.57774 16.1851L6.27342 12.129C6.29511 12.0025 6.30595 11.9393 6.29862 11.8788C6.29212 11.8252 6.27528 11.7734 6.24902 11.7263C6.21937 11.673 6.17341 11.6282 6.0815 11.5387L3.13315 8.66702C2.8224 8.36436 2.66703 8.21302 2.64812 8.06985C2.63167 7.94529 2.67231 7.81995 2.75872 7.72874C2.85804 7.6239 3.07266 7.59253 3.50189 7.5298L7.12184 7.00068M2.99976 2.50024L17.9998 17.5002" stroke="#A4A7AE" strokeWidth="1.66667" strokeLinecap="round" strokeLinejoin="round"/>
                                                    </svg>
                                                    <p className="text-md leading-md font-semibold text-[#A4A7AE]">
                                                        {screenSize?.bw375640 ? "":" Hết lượt dùng sao"}
                                                    </p>
                                                </button>
                                        }
                                        <div className="xem_ket_qua gap-sm flex flex-row max-w-[397px] w-full py-lg px-[18px] rounded-md border border-[#8EE5BA] text-center bg-[#299D55]">

                                            {
                                                (isViewAnswer && indexQuestionActive === 2) ?
                                                    <button onClick={finishStreak} className="text-md leading-md font-semibold text-white w-full text-center flex justify-center gap-sm items-center">Hoàn thành</button>
                                                    :
                                                    <button onClick={ViewAnswerAndNextQuestion} className="text-md leading-md font-semibold text-white w-full text-center flex justify-center gap-sm items-center">{isViewAnswer ? 'Câu tiếp theo' : 'Xem kết quả'}
                                                        {
                                                            isViewAnswer?
                                                                <svg xmlns="http://www.w3.org/2000/svg" width="21" height="20" viewBox="0 0 21 20" fill="none">
                                                                    <path d="M4.66602 9.99935H16.3327M16.3327 9.99935L10.4993 4.16602M16.3327 9.99935L10.4993 15.8327" stroke="white" strokeWidth="1.66667" strokeLinecap="round" strokeLinejoin="round"/>
                                                                </svg>
                                                                : ""
                                                        }
                                                    </button>
                                            }
                                        </div>
                                    </div>

                                </div>
                            </>
                            :
                            //Chấp nhận thử thách hay không
                            <>
                                <div className="container_streak w-full py-3xl px-xl flex flex-col items-center overflow-auto pt-[76px] pb-[96px]">
                                    <div className="flex flex-col justify-center gap-y-xs">
                                        <div className="flex flex-row gap-md justify-center items-center">
                                            <p className="text-lg leading-lg font-semibold text-brand-600">Cơ hội nhận thêm</p>
                                            <div className="flex flex-row gap-xs py-xs pl-md pr-[10px] items-center bg-utility-brand-75 rounded-md">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 12 12" fill="none">
                                                    <path fillRule="evenodd" clipRule="evenodd" d="M9.18463 1.00092C9.68153 0.987068 10.1716 1.11002 10.5308 1.46918C10.89 1.82837 11.0129 2.31847 10.9991 2.81537C10.9853 3.31086 10.8363 3.86113 10.5948 4.42474C10.3795 4.92707 10.0815 5.45911 9.71344 5.99994C10.0815 6.54075 10.3795 7.07282 10.5948 7.57514C10.8363 8.13874 10.9852 8.68903 10.9991 9.18451C11.0129 9.68143 10.89 10.1715 10.5308 10.5307C10.1716 10.8899 9.68156 11.0128 9.18463 10.999C8.68915 10.9851 8.13886 10.8362 7.57526 10.5947C7.07295 10.3794 6.54087 10.0814 6.00006 9.71332C5.45923 10.0814 4.9272 10.3794 4.42487 10.5947C3.86125 10.8362 3.31098 10.9851 2.81549 10.999C2.31859 11.0128 1.82849 10.8899 1.4693 10.5307C1.11014 10.1715 0.98719 9.68141 1.00104 9.18451C1.01488 8.68903 1.16379 8.13874 1.40534 7.57514C1.62059 7.07288 1.91822 6.54068 2.2862 5.99994C1.91823 5.45921 1.62058 4.92699 1.40534 4.42474C1.1638 3.86114 1.01486 3.31084 1.00104 2.81537C0.987202 2.31847 1.11011 1.82837 1.4693 1.46918C1.82849 1.10999 2.31859 0.98708 2.81549 1.00092C3.31096 1.01474 3.86126 1.16368 4.42487 1.40521C4.92711 1.62046 5.45933 1.91811 6.00006 2.28607C6.54081 1.91809 7.073 1.62047 7.57526 1.40521C8.13886 1.16367 8.68915 1.01475 9.18463 1.00092ZM2.92145 6.85346C2.6754 7.24222 2.47527 7.61801 2.32477 7.96918C2.11281 8.46375 2.00969 8.88533 2.00055 9.21234C1.99147 9.53782 2.0747 9.72102 2.17682 9.82318C2.27896 9.92532 2.46215 10.0085 2.78766 9.99945C3.11467 9.99033 3.53624 9.88719 4.03082 9.67523C4.38196 9.52475 4.75734 9.32408 5.14606 9.07806C4.75171 8.75943 4.35842 8.40713 3.97565 8.02435C3.5927 7.64141 3.2402 7.24799 2.92145 6.85346ZM9.07819 6.85346C8.75947 7.24793 8.40737 7.64146 8.02448 8.02435C7.64158 8.40725 7.24805 8.75935 6.85358 9.07806C7.2424 9.32416 7.61808 9.5247 7.9693 9.67523C8.46387 9.88719 8.88546 9.99031 9.21246 9.99945C9.53801 10.0085 9.72116 9.92533 9.8233 9.82318C9.92545 9.72104 10.0087 9.53789 9.99957 9.21234C9.99044 8.88533 9.88731 8.46375 9.67535 7.96918C9.52483 7.61796 9.32429 7.24228 9.07819 6.85346ZM2.78766 2.00043C2.46215 1.99136 2.27896 2.07456 2.17682 2.1767C2.07468 2.27884 1.99149 2.46203 2.00055 2.78754C2.00968 3.11454 2.11282 3.53613 2.32477 4.0307C2.47522 4.38175 2.67552 4.75731 2.92145 5.14594C3.24015 4.75148 3.59278 4.3584 3.97565 3.97553C4.35852 3.59266 4.7516 3.24003 5.14606 2.92133C4.75744 2.6754 4.38187 2.4751 4.03082 2.32465C3.53625 2.1127 3.11466 2.00955 2.78766 2.00043ZM9.21246 2.00043C8.88546 2.00956 8.46387 2.11269 7.9693 2.32465C7.61813 2.47515 7.24234 2.67528 6.85358 2.92133C7.24811 3.24008 7.64153 3.59258 8.02448 3.97553C8.40725 4.3583 8.75955 4.75158 9.07819 5.14594C9.3242 4.75722 9.52487 4.38183 9.67535 4.0307C9.88732 3.53611 9.99045 3.11455 9.99957 2.78754C10.0086 2.46203 9.92545 2.27884 9.8233 2.1767C9.72114 2.07458 9.53795 1.99135 9.21246 2.00043Z" fill="#7A5AF8"/>
                                                    <circle cx="6" cy="6" r="1" fill="#EBE9FE"/>
                                                </svg>
                                                <p className="font-medium text-sm leading-sm text-utility-brand-700">{questions && questions.length === 3 ? questions[2].exercise_type.point : ''}</p>
                                            </div>
                                        </div>
                                        <div className="max-w-[622px] w-full flex flex-row justify-center items-center text-center">
                                            <p className={clsx("font-normal text-tertiary-600",screenSize?.lte960 ? "text-sm leading-sm":"text-md leading-md")}>Tiếp theo là <strong>câu hỏi khó</strong> á nha. Tự tin thử sức đi, Ba tin tụ bây! Đã chơi rồi mà thoát là công sức HAI câu trước tao không tính cho tụ bây đâu</p>
                                        </div>
                                    </div>
                                </div>
                                <div className="footer_streak w-full h-fit py-3xl px-xl fixed bg-white z-50 bottom-0 left-0 border-t border-primary">
                                    <div className="items-center justify-center flex flex-row gap-x-[16px]">
                                        <button onClick={finishStreak} className="sao_hi_vong w-[303px] py-lg px-[18px] rounded-md border border-[#E9EAEB] text-center flex flex-row items-center justify-center gap-sm">
                                            <p className={clsx("font-semibold text-button-secondary-fg",screenSize?.lte960 ? "text-sm leading-sm ":"text-md leading-md ")}>Hoàn thành streak</p>
                                        </button>
                                        <button onClick={handelContinueStreak} className="sao_hi_vong w-[303px] py-lg px-[18px] rounded-md border border-[#E9EAEB] bg-primary-bg text-center flex flex-row items-center justify-center gap-sm" >
                                            <p className={clsx("font-semibold text-white",screenSize?.lte960 ? "text-sm leading-sm":"text-md leading-md ")}>Thử sức</p>
                                        </button>
                                    </div>

                                </div>
                            </>

                    }


                </div>
                {
                    isViewFullImage ?  <ViewFullImageModal isOpen={isViewFullImage} onClose={closeViewFullImage} imagePath={questionActive?.image_path}></ViewFullImageModal> : ''
                }
                {
                    isViewStarPoint ?  <StarPointPopup
                        isOpen={isViewStarPoint}
                        onClose={() => {setIsViewStarPoint(false)}}
                        onSendData={(data) => {
                            let a = {...userAnswer,is_star_point: data}
                            setUserAnswer(a);
                        }}
                    ></StarPointPopup> : ''
                }
                {
                    isViewOutPopup ?  <OutStreakPopup
                        isOpen={isViewOutPopup}
                        onClose={() => {setIsViewOutPopup(false)}}
                        onSendData={(data) => {
                           if (data) {
                               router.push("/quan-ly");
                           }
                        }}
                    ></OutStreakPopup> : ''
                }
                {
                    isContinueStreak ?  <ContinueStreak
                        isOpen={isContinueStreak}
                        onClose={() => {setIsContinueStreak(false)}}
                        onSendData={(data) => {
                            if (data) {
                                router.push("/quan-ly");
                            }
                        }}
                    ></ContinueStreak> : ''
                }
                {
                    isViewReportStreak ?  <ReportStreak
                        isOpen={isViewReportStreak}
                        onClose={() => {setIsViewReportStreak(false)}}
                        onSendData={(data) => {
                            if (data) {
                                toast.success('Đã gửi báo cáo!');
                            }
                        }}
                        data={questionActive}
                    ></ReportStreak> : ''
                }



            </>

        );

    }

}
