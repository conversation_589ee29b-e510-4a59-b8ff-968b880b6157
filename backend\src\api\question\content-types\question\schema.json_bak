{"kind": "collectionType", "collectionName": "questions", "info": {"singularName": "question", "pluralName": "questions", "displayName": "Question", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"questions_topics": {"type": "relation", "relation": "manyToMany", "target": "api::questions-topic.questions-topic", "inversedBy": "questions"}, "image_path": {"type": "string"}, "content": {"type": "text"}, "A": {"type": "string"}, "B": {"type": "string"}, "C": {"type": "string"}, "D": {"type": "string"}, "explain": {"type": "text"}, "exercise_type": {"type": "relation", "relation": "manyToOne", "target": "api::exercise-type.exercise-type", "inversedBy": "questions"}, "type": {"type": "enumeration", "enum": ["TN_4", "TN_2", "TN_value"]}, "grade": {"type": "relation", "relation": "manyToOne", "target": "api::grade.grade", "inversedBy": "questions"}, "chapter": {"type": "relation", "relation": "manyToOne", "target": "api::chapter.chapter", "inversedBy": "questions"}, "knowledge_questions": {"type": "relation", "relation": "oneToMany", "target": "api::exercise.exercise", "mappedBy": "question"}, "question_status": {"type": "enumeration", "enum": ["todo", "checking", "approval", "reject"]}, "correct_answer": {"type": "string"}}}