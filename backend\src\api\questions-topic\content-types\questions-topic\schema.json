{"kind": "collectionType", "collectionName": "questions_topics", "info": {"singularName": "questions-topic", "pluralName": "questions-topics", "displayName": "Questions_topic", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"questions": {"type": "relation", "relation": "manyToMany", "target": "api::question.question", "mappedBy": "questions_topics"}, "name": {"type": "string"}, "value": {"type": "string"}, "description": {"type": "string"}, "time_limit": {"type": "integer"}}}