"use client";

import React, {useEffect, useRef, useState} from "react";
import clsx from "clsx";
import LineDivider from "@/components/icons/LineDivider";
import strapi from "@/app/api/strapi";
import Cookies from "universal-cookie";
import {CommonUtil} from "@/utils/CommonUtil";
import {useScreenSize} from "@/hooks/useScreenSize";

/**
 * Popup hóa đơn mua khóa học
 * @param isOpen
 * @param onClose
 * @constructor
 */
const ContinueStreak = ({isOpen, onClose, onSendData}) => {
    if (!isOpen) return;
    const [isUse, setIsUse] = useState(false);
    const handleSend = () => {
        onSendData?.(true);
        onClose?.();
    };
    return (
        <div className="fixed inset-0  flex items-center justify-center bg-[#000000] bg-opacity-60 px-4"
             style={{zIndex: 120}}
            tabIndex={-1}
            aria-modal="true"
            role="dialog">
            <div className="bg-[#FFFFFF] rounded-2xl  shadow-xl w-[400px] relative max-h-screen overflow-y-auto">
                <div className="px-3xl pt-3xl">
                    <div className="header_model mb-xl">
                        <div className="w-full h-full relative flex justify-center">
                            <div className="w-[48px] h-[48px] p-[12px] flex justify-center items-center rounded-full bg-[#DCFAEC]">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path d="M9.35493 21C10.0601 21.6224 10.9863 22 12.0008 22C13.0152 22 13.9414 21.6224 14.6466 21M18.0008 8C18.0008 6.4087 17.3686 4.88258 16.2434 3.75736C15.1182 2.63214 13.5921 2 12.0008 2C10.4095 2 8.88333 2.63214 7.75811 3.75736C6.63289 4.88258 6.00075 6.4087 6.00075 8C6.00075 11.0902 5.22122 13.206 4.35042 14.6054C3.61588 15.7859 3.24861 16.3761 3.26208 16.5408C3.27699 16.7231 3.31561 16.7926 3.46253 16.9016C3.59521 17 4.19334 17 5.38961 17H18.6119C19.8082 17 20.4063 17 20.539 16.9016C20.6859 16.7926 20.7245 16.7231 20.7394 16.5408C20.7529 16.3761 20.3856 15.7859 19.6511 14.6054C18.7803 13.206 18.0008 11.0902 18.0008 8Z" stroke="#299D55" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                            </div>
                            {/*<div className="button_close absolute right-0 top-0 cursor-pointer" onClick={onClose}>*/}
                            {/*    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">*/}
                            {/*        <path d="M18 6L6 18M6 6L18 18" stroke="#A4A7AE" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>*/}
                            {/*    </svg>*/}
                            {/*</div>*/}
                        </div>
                    </div>
                    <div className="content_ flex flex-col text-center w-full">
                        <p className=" text-primary-900 text-lg leading-lg font-semibold w-full">
                            Tiếp tục làm câu tiếp theo?
                        </p>
                        <p className="text-tertiary-600 text-sm leading-sm font-normal">
                            Hệ thống đã ghi nhận các câu trước, mày bỏ chút kiến thức làm tiếp là xong rồi
                        </p>
                    </div>
                </div>

                <div className="footer_ p-3xl gap-lg flex justify-center">
                    {/*<button onClick={handleSend}  className="px-xl py-[10px] text-[#414651] w-[170px] text-md leading-md font-semibold rounded-md border border-secondary">*/}
                    {/*    Xíu em quay lại*/}
                    {/*</button>*/}
                    <button onClick={onClose} className="px-xl py-[10px] text-white w-full text-md leading-md font-semibold bg-[#299D55] rounded-md">
                        Tiếp tục
                    </button>
                </div>
            </div>

        </div>
    );
};

export default ContinueStreak;
