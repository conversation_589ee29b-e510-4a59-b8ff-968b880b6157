{"kind": "collectionType", "collectionName": "reports", "info": {"singularName": "report", "pluralName": "reports", "displayName": "report"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"reason": {"type": "string"}, "question": {"type": "relation", "relation": "oneToOne", "target": "api::question.question"}, "description": {"type": "text"}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}}}