export default {
    routes: [
        {
            method: 'GET',
            path: '/streaks/raw-total',
            handler: 'streak.rawTotal',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/streaks/get-streak-by-user',
            handler: 'streak.getStreakByUser',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/streaks/get-total-rollup',
            handler: 'streak.getTotalRollup',
            config: {
                policies: [],
                middlewares: [],
            },
        },
        {
            method: 'POST',
            path: '/streaks/get-data-finish',
            handler: 'streak.getDataFinish',
            config: {
                policies: [],
                middlewares: [],
            },
        },
    ],
};