"use client";

import React, {useEffect, useRef, useState} from "react";
import LineDivider from "@/components/icons/LineDivider";
import {Swiper, SwiperSlide} from "swiper/react";
import 'swiper/css';
import 'swiper/css/pagination';
import {Pagination} from "swiper/modules";
import clsx from "clsx";

/**
 * Popup hóa đơn mua khóa học
 * @param isOpen
 * @param onClose
 * @constructor
 */
const TutorialStreak = ({isOpen, onClose, onSendData}) => {
    if (!isOpen) return;
    const [start, setStart] = useState(false);
    const [swiperInstance, setSwiperInstance] = useState(null);
    const [isEndSlide, setIsEndSlide] = useState(false);
    const [isStartSlide, setIsStartSlide] = useState(true);
    const [images, setImages] = useState([
        '/images/mascot-hero.png',
        '/images/mascot-hero.png',
        '/images/mascot-hero.png',
        '/images/mascot-hero.png',
    ]);
    useEffect(() => {
        const handleKeyDown = (e) => {
            if (e.key === "Escape") onClose();
        };
        window.addEventListener("keydown", handleKeyDown);
        return () => window.removeEventListener("keydown", handleKeyDown);
    }, [isOpen, onClose]);
    const nextSlide = () => {
        if (isEndSlide){
            onClose?.();
        }else {
            swiperInstance?.slideNext();
            let isEnd = false;
            if (swiperInstance && swiperInstance.isEnd) {
                isEnd = true;
            }
            setIsStartSlide(false);
            setIsEndSlide(isEnd);
        }

    };
    const backSlide = () => {
        setIsEndSlide(false);
        swiperInstance?.slidePrev();
        if (swiperInstance && swiperInstance.isBeginning) {
            setIsStartSlide(true);
        }
    };
    const handleSend = () => {
        onSendData?.(true);
        onClose?.();
    };
    const handleStart = () => {
      setStart(true);
    }
    if (!start) {
        return (
            <div className="fixed inset-0  flex items-center justify-center bg-[#000000] bg-opacity-60 px-4"
                 style={{zIndex: 120}}
                 tabIndex={-1}
                 aria-modal="true"
                 role="dialog">
                <div className="bg-[#FFFFFF] rounded-xl p-3xl shadow-xl w-[490px] gap-y-3xl relative max-h-screen overflow-y-auto">
                    <div className="header_ flex flex-col text-center w-full">
                        <p className="w-full text-xl font-semibold leading-xl text-primary-900">Chào mừng đến thử thách Streak 🎉</p>
                        <p className="w-full text-md font-normal leading-md text-tertiary-600">Khám phá sử dụng Streak đã sẵn sàng trong phần đầu khoá học – đừng bỏ lỡ nhé!</p>
                    </div>
                    <div className="conent flex flex-col text-left w-full gap-y-2xl mt-3xl">
                        <div className="img_ w-full aspect-[441.00/220.50] rounded-xl bg-[#D9D9D9]">
                            <img src="" className="w-full h-full object-cover no-repeat"></img>
                        </div>
                        <div className="flex flex-col w-full">
                            <p className="w-full line-clamp-1 font-medium text-md leading-md text-tertiary-600">Lorem ipsum dolor sit amet, consectetura</p>
                            <p className="w-full line-clamp-1 font-medium text-md leading-md text-tertiary-600">Lorem ipsum dolor sit amet, consectetura</p>
                            <p className="w-full line-clamp-1 font-medium text-md leading-md text-tertiary-600">Lorem ipsum dolor sit amet, consectetura</p>
                        </div>
                    </div>
                    <div className="footer flex flex-col gap-y-lg mt-3xl">
                       <button className="w-full py-[10px] px-xl rounded-md bg-primary-bg text-white text-md leading-md font-semibold" onClick={handleStart}>Khám phá ngay</button>
                       <button className="w-full py-[10px] px-xl text-[#535862] text-sm leading-sm font-semibold" onClick={onClose}>Bỏ qua</button>
                    </div>
                </div>

            </div>
        );
    }else  {
        return (
            <div className="fixed inset-0  flex items-center justify-center bg-[#000000] bg-opacity-60 px-4"
                 style={{zIndex: 120}}
                 tabIndex={-1}
                 aria-modal="true"
                 role="dialog">
                <div className="bg-[#FFFFFF] rounded-xl p-3xl shadow-xl w-[490px] gap-y-3xl relative max-h-screen overflow-y-auto">
                    <div className="bg-white rounded-xl w-full overflow-hidden mb-3xl">
                        {/* Slider Section */}
                        <div className="">
                            <Swiper
                                onSwiper={(swiper) => setSwiperInstance(swiper)}
                                pagination={{ clickable: true }}
                                modules={[Pagination]}
                                className="w-full aspect-[441.00/220.50]"
                            >
                                {images.map((src, index) => (
                                    <SwiperSlide key={index}>
                                        <div className="bg-[#D9D9D9] flex justify-center items-center rounded-xl">
                                            <img
                                                src={src}
                                                alt={`Ảnh ${index}`}
                                                className="h-full w-full object-cover rounded-xl"
                                            />
                                        </div>
                                    </SwiperSlide>
                                ))}
                            </Swiper>
                        </div>

                        {/* Text content */}
                        <div className="text-center w-full flex flex-col mt-xl">
                            <p className="w-full text-xl font-semibold leading-xl text-primary-900">Lorem ipsum dolor sit amet consectetur</p>
                            <p className="w-full text-md font-normal leading-md text-tertiary-600">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua</p>
                        </div>
                    </div>
                    {/* Action buttons */}
                    <LineDivider></LineDivider>
                    <div className="flex flex-row justify-between items-center mt-3xl gap-xl">
                        <div className="flex flex-row justify-start ">
                            <button className={clsx("py-[10px] px-xl items-center rounded-md w-[124x] flex flex-row gap-sm border border-secondary",isStartSlide ? "hidden" : "")} onClick={backSlide}>
                                <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 21 21" fill="none">
                                    <path d="M16.3334 10.7493H4.66675M4.66675 10.7493L10.5001 16.5827M4.66675 10.7493L10.5001 4.91602" stroke="#414651" strokeWidth="1.66667" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                               <p className="text-md text-button-secondary-fg leading-md font-semibold">Quay lại</p>
                            </button>
                        </div>
                        <div className="flex flex-row justify-end items-center gap-xl">
                            <button className={clsx("text-sm leading-sm font-semibold text-[#535862]", isEndSlide ? "hidden" : "")} onClick={onClose}>Bỏ qua</button>
                            <button className="py-[10px] px-xl items-center rounded-md bg-primary-bg w-[136x] flex flex-row gap-sm" onClick={nextSlide}>
                                <p className="text-white text-md leading-md font-semibold">{isEndSlide ? "Chơi thôi" : "Tiếp theo"}</p>
                                {
                                    isEndSlide ?
                                        ""
                                        :
                                        <svg xmlns="http://www.w3.org/2000/svg" width="21" height="21" viewBox="0 0 21 21" fill="none">
                                            <path d="M4.66675 10.7493H16.3334M16.3334 10.7493L10.5001 4.91602M16.3334 10.7493L10.5001 16.5827" stroke="white" strokeWidth="1.66667" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                }

                            </button>
                        </div>

                    </div>
                </div>

            </div>
        );
    }

};

export default TutorialStreak;
